const {
  Client,
  GatewayIntentBits,
  EmbedBuilder,
  SlashCommandBuilder,
  REST,
  Routes,
} = require("discord.js");
const config = require("./config");

class DiscordBot {
  constructor() {
    this.client = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
      ],
    });

    this.channelId = config.discord.channelId;
    this.guildId = config.discord.guildId;
    this.isReady = false;
    this.simulator = null; // Will be set by the main bot
    this.strategy = null; // Will be set by the main bot

    this.setupEventHandlers();
    this.setupCommands();
  }

  /**
   * Setup Discord client event handlers
   */
  setupEventHandlers() {
    this.client.once("ready", () => {
      console.log(`Discord bot logged in as ${this.client.user.tag}`);
      this.isReady = true;
      this.registerSlashCommands();
      this.validateBotSetup();
    });

    this.client.on("interactionCreate", async (interaction) => {
      if (!interaction.isChatInputCommand()) return;

      await this.handleSlashCommand(interaction);
    });

    this.client.on("error", (error) => {
      console.error("Discord client error:", error);
    });
  }

  /**
   * Setup slash commands
   */
  setupCommands() {
    this.commands = [
      new SlashCommandBuilder()
        .setName("balance")
        .setDescription("Get current trading balance"),

      new SlashCommandBuilder()
        .setName("profit")
        .setDescription("Get current profit/loss"),

      new SlashCommandBuilder()
        .setName("status")
        .setDescription("Get bot status and current positions"),

      new SlashCommandBuilder()
        .setName("strategy")
        .setDescription("Get current strategy conditions for a symbol")
        .addStringOption((option) =>
          option
            .setName("symbol")
            .setDescription("Trading pair symbol (e.g., BTCUSDT)")
            .setRequired(true)
        ),
    ];
  }

  /**
   * Register slash commands with Discord
   */
  async registerSlashCommands() {
    try {
      const rest = new REST({ version: "10" }).setToken(config.discord.token);

      console.log("Registering Discord slash commands...");

      await rest.put(
        Routes.applicationGuildCommands(this.client.user.id, this.guildId),
        { body: this.commands.map((command) => command.toJSON()) }
      );

      console.log("Discord slash commands registered successfully");
    } catch (error) {
      console.error("Error registering slash commands:", error);
    }
  }

  /**
   * Handle slash command interactions
   */
  async handleSlashCommand(interaction) {
    const { commandName } = interaction;

    try {
      switch (commandName) {
        case "balance":
          await this.handleBalanceCommand(interaction);
          break;
        case "profit":
          await this.handleProfitCommand(interaction);
          break;
        case "status":
          await this.handleStatusCommand(interaction);
          break;
        case "strategy":
          await this.handleStrategyCommand(interaction);
          break;
        default:
          await interaction.reply("Unknown command");
      }
    } catch (error) {
      console.error("Error handling slash command:", error);
      await interaction.reply("An error occurred while processing the command");
    }
  }

  /**
   * Handle balance command
   */
  async handleBalanceCommand(interaction) {
    if (!this.simulator) {
      await interaction.reply("Simulator not initialized");
      return;
    }

    const balance = this.simulator.currentBalance.toFixed(2);
    await interaction.reply(`💰 Current Balance: $${balance}`);
  }

  /**
   * Handle profit command
   */
  async handleProfitCommand(interaction) {
    if (!this.simulator) {
      await interaction.reply("Simulator not initialized");
      return;
    }

    const profit = (
      this.simulator.currentBalance - this.simulator.startingBalance
    ).toFixed(2);
    const profitPercent = (
      (profit / this.simulator.startingBalance) *
      100
    ).toFixed(2);
    const emoji = profit >= 0 ? "📈" : "📉";

    await interaction.reply(
      `${emoji} Profit/Loss: $${profit} (${profitPercent}%)`
    );
  }

  /**
   * Handle status command
   */
  async handleStatusCommand(interaction) {
    if (!this.simulator) {
      await interaction.reply("Simulator not initialized");
      return;
    }

    const embed = new EmbedBuilder()
      .setTitle("🤖 Bot Status")
      .setColor(this.simulator.isHavingOrder ? 0xff6b6b : 0x4ecdc4)
      .addFields(
        {
          name: "Balance",
          value: `$${this.simulator.currentBalance.toFixed(2)}`,
          inline: true,
        },
        {
          name: "Starting Balance",
          value: `$${this.simulator.startingBalance.toFixed(2)}`,
          inline: true,
        },
        {
          name: "P&L",
          value: `$${(
            this.simulator.currentBalance - this.simulator.startingBalance
          ).toFixed(2)}`,
          inline: true,
        },
        {
          name: "Active Order",
          value: this.simulator.isHavingOrder ? "Yes" : "No",
          inline: true,
        }
      )
      .setTimestamp();

    if (this.simulator.isHavingOrder && this.simulator.order) {
      const order = this.simulator.order;
      embed.addFields(
        { name: "Order Type", value: order.type.toUpperCase(), inline: true },
        { name: "Symbol", value: order.pair, inline: true },
        { name: "Entry Price", value: `$${order.price}`, inline: true },
        { name: "Stop Loss", value: `$${order.stoploss}`, inline: true },
        { name: "Take Profit", value: `$${order.takeProfit}`, inline: true },
        { name: "Amount", value: order.amount.toString(), inline: true }
      );
    }

    await interaction.reply({ embeds: [embed] });
  }

  /**
   * Handle strategy command
   */
  async handleStrategyCommand(interaction) {
    const symbol = interaction.options.getString("symbol").toUpperCase();

    if (!this.strategy) {
      await interaction.reply("❌ Strategy not initialized");
      return;
    }

    try {
      // Get strategy status for the symbol (now async for on-demand fetching)
      const strategyStatus = await this.strategy.getStrategyStatus(symbol);

      if (!strategyStatus.analysis.valid) {
        await interaction.reply(
          `❌ No valid analysis available for ${symbol}\nReason: ${
            strategyStatus.analysis.reason || "Unknown"
          }`
        );
        return;
      }

      // Create detailed strategy embed
      const embed = await this.createStrategyEmbed(symbol, strategyStatus);
      await interaction.reply({ embeds: [embed] });
    } catch (error) {
      console.error("Error in handleStrategyCommand:", error);
      await interaction.reply(
        `❌ Error analyzing strategy for ${symbol}: ${error.message}`
      );
    }
  }

  /**
   * Create strategy analysis embed
   */
  async createStrategyEmbed(symbol, strategyStatus) {
    const {
      analysis,
      lastUpdate: lastUpdateTimestamp,
      dataPoints,
    } = strategyStatus;
    const { indicators, conditions, currentPrice, timestamp } = analysis;

    // Determine overall signal color
    const signalColor = conditions.signal === "BUY" ? 0x00ff00 : 0xffaa00;
    const signalEmoji = conditions.signal === "BUY" ? "🟢" : "🟡";

    // Format last update time
    const lastUpdate = lastUpdateTimestamp
      ? new Date(lastUpdateTimestamp).toLocaleString()
      : "N/A";

    const embed = new EmbedBuilder()
      .setTitle(`📊 Strategy Analysis: ${symbol}`)
      .setColor(signalColor)
      .setDescription(`Last updated: ${lastUpdate}`)
      .setTimestamp(new Date(timestamp));

    // Add current price and signal
    embed.addFields(
      {
        name: "Current Price",
        value: `$${currentPrice?.toFixed(4) || "N/A"}`,
        inline: true,
      },
      {
        name: "Signal",
        value: `${signalEmoji} ${conditions.signal}`,
        inline: true,
      },
      {
        name: "Data Points",
        value: dataPoints.toString(),
        inline: true,
      }
    );

    // Add strategy conditions
    let conditionsText = "";
    Object.entries(conditions).forEach(([key, condition]) => {
      if (
        key !== "allConditionsMet" &&
        key !== "signal" &&
        condition.description
      ) {
        const emoji = condition.met ? "✅" : "❌";
        conditionsText += `${emoji} ${condition.description}\n`;
      }
    });

    if (conditionsText) {
      embed.addFields({
        name: "Strategy Conditions",
        value: conditionsText,
        inline: false,
      });
    }

    // Add technical indicators if available
    if (indicators) {
      const indicatorFields = [];

      // Bollinger Bands
      if (indicators.bollingerBands) {
        const bb = indicators.bollingerBands;
        indicatorFields.push({
          name: "Bollinger Bands",
          value: `Upper: $${bb.upper.toFixed(2)}\nMiddle: $${bb.middle.toFixed(
            2
          )}\nLower: $${bb.lower.toFixed(2)}`,
          inline: true,
        });
      }

      // BBW (Bollinger Band Width)
      if (indicators.bbw) {
        indicatorFields.push({
          name: "BB Width",
          value: indicators.bbw.toFixed(4),
          inline: true,
        });
      }

      // RSI
      if (indicators.rsi) {
        indicatorFields.push({
          name: "RSI",
          value: indicators.rsi.toFixed(2),
          inline: true,
        });
      }

      // MACD
      if (indicators.macd) {
        const macd = indicators.macd;
        indicatorFields.push({
          name: "MACD",
          value: `MACD: ${macd.MACD.toFixed(4)}\nSignal: ${macd.signal.toFixed(
            4
          )}\nHist: ${macd.histogram.toFixed(4)}`,
          inline: true,
        });
      }

      // Volume
      if (indicators.currentVolume && indicators.averageVolume) {
        indicatorFields.push({
          name: "Volume",
          value: `Current: ${indicators.currentVolume.toFixed(
            2
          )}\nAvg: ${indicators.averageVolume.toFixed(2)}\nRatio: ${(
            indicators.currentVolume / indicators.averageVolume
          ).toFixed(2)}x`,
          inline: true,
        });
      }

      if (indicatorFields.length > 0) {
        embed.addFields({ name: "Technical Indicators", value: "\u200B" });
        embed.addFields(...indicatorFields);
      }
    }

    return embed;
  }

  /**
   * Send a notification message to the configured channel
   */
  async sendNotification(message, embed = null) {
    if (!this.isReady || !this.channelId) {
      console.log("Discord bot not ready or channel not configured");
      return;
    }

    try {
      // Try to get channel from cache first
      let channel = this.client.channels.cache.get(this.channelId);

      // If not in cache, try to fetch it
      if (!channel) {
        channel = await this.client.channels.fetch(this.channelId);
      }

      // Check if channel exists and bot has access
      if (!channel) {
        console.error(
          `Channel ${this.channelId} not found or bot doesn't have access`
        );
        return;
      }

      // Check if it's a text channel
      if (!channel.isTextBased()) {
        console.error(`Channel ${this.channelId} is not a text channel`);
        return;
      }

      if (embed) {
        await channel.send({ content: message, embeds: [embed] });
      } else {
        await channel.send(message);
      }
    } catch (error) {
      console.error("Error sending Discord notification:", error);
      console.error("Channel ID:", this.channelId);
      console.error("Bot user ID:", this.client.user?.id);

      // Try alternative approach - send to any available channel as fallback
      if (error.code === 50001) {
        console.log("Attempting to find an alternative channel...");
        await this.findAndUseAlternativeChannel(message, embed);
      }
    }
  }

  /**
   * Find and use an alternative channel when the configured one fails
   */
  async findAndUseAlternativeChannel(message, embed = null) {
    try {
      // Get all text channels the bot can see
      const guild = this.client.guilds.cache.get(this.guildId);
      if (!guild) {
        console.error("Guild not found");
        return;
      }

      const textChannels = guild.channels.cache.filter(
        (channel) =>
          channel.isTextBased() &&
          channel
            .permissionsFor(this.client.user)
            .has(["SendMessages", "ViewChannel"])
      );

      if (textChannels.size === 0) {
        console.error("No accessible text channels found");
        return;
      }

      // Use the first available channel
      const fallbackChannel = textChannels.first();
      console.log(
        `Using fallback channel: ${fallbackChannel.name} (${fallbackChannel.id})`
      );

      const fallbackMessage = `⚠️ **Bot Configuration Issue**\nConfigured channel not accessible. Using this channel temporarily.\nPlease check bot permissions for channel ID: ${this.channelId}\n\n${message}`;

      if (embed) {
        await fallbackChannel.send({
          content: fallbackMessage,
          embeds: [embed],
        });
      } else {
        await fallbackChannel.send(fallbackMessage);
      }
    } catch (error) {
      console.error("Error finding alternative channel:", error);
    }
  }

  /**
   * Send trading signal notification
   */
  async sendTradingSignal(signalData) {
    const { type, symbol, price, stoploss, takeProfit, amount, conditions } =
      signalData;

    const embed = new EmbedBuilder()
      .setTitle(`🚨 ${type.toUpperCase()} SIGNAL DETECTED`)
      .setColor(type === "BUY" ? 0x00ff00 : 0xff0000)
      .addFields(
        { name: "Symbol", value: symbol, inline: true },
        { name: "Entry Price", value: `$${price}`, inline: true },
        { name: "Amount", value: amount.toString(), inline: true },
        { name: "Stop Loss", value: `$${stoploss}`, inline: true },
        { name: "Take Profit", value: `$${takeProfit}`, inline: true },
        { name: "\u200B", value: "\u200B", inline: true }
      )
      .setTimestamp();

    if (conditions) {
      let conditionsText = "";
      Object.entries(conditions).forEach(([key, condition]) => {
        if (key !== "allConditionsMet" && key !== "signal") {
          const emoji = condition.met ? "✅" : "❌";
          conditionsText += `${emoji} ${condition.description}\n`;
        }
      });

      if (conditionsText) {
        embed.addFields({ name: "Strategy Conditions", value: conditionsText });
      }
    }

    await this.sendNotification("", embed);
  }

  /**
   * Send price alert notification
   */
  async sendPriceAlert(symbol, price, alertType) {
    const embed = new EmbedBuilder()
      .setTitle(`📊 ${symbol} Price Alert`)
      .setColor(0xffaa00)
      .addFields(
        { name: "Symbol", value: symbol, inline: true },
        { name: "Current Price", value: `$${price}`, inline: true },
        { name: "Alert Type", value: alertType, inline: true }
      )
      .setTimestamp();

    await this.sendNotification("", embed);
  }

  /**
   * Send profit/loss notification
   */
  async sendProfitLossNotification(orderData, currentPrice, profit) {
    const { type, symbol, price: entryPrice } = orderData;
    const isProfit = profit >= 0;

    const embed = new EmbedBuilder()
      .setTitle(
        isProfit ? "💰 TAKE PROFIT SUCCESSFUL" : "🛑 STOP LOSS TRIGGERED"
      )
      .setColor(isProfit ? 0x00ff00 : 0xff0000)
      .addFields(
        { name: "Symbol", value: symbol, inline: true },
        { name: "Order Type", value: type.toUpperCase(), inline: true },
        { name: "Entry Price", value: `$${entryPrice}`, inline: true },
        { name: "Exit Price", value: `$${currentPrice}`, inline: true },
        { name: "P&L", value: `$${profit.toFixed(2)}`, inline: true },
        { name: "\u200B", value: "\u200B", inline: true }
      )
      .setTimestamp();

    await this.sendNotification("", embed);
  }

  /**
   * Validate bot setup and permissions
   */
  async validateBotSetup() {
    try {
      console.log("🔍 Validating Discord bot setup...");

      // Check if guild exists
      const guild = this.client.guilds.cache.get(this.guildId);
      if (!guild) {
        console.error(
          `❌ Guild ${this.guildId} not found. Bot may not be in the server.`
        );
        return;
      }
      console.log(`✅ Guild found: ${guild.name}`);

      // Check if channel exists and is accessible
      try {
        const channel = await this.client.channels.fetch(this.channelId);
        if (channel) {
          console.log(`✅ Channel found: ${channel.name} (${channel.type})`);

          // Check permissions
          const permissions = channel.permissionsFor(this.client.user);
          const requiredPerms = ["ViewChannel", "SendMessages", "EmbedLinks"];

          for (const perm of requiredPerms) {
            if (permissions.has(perm)) {
              console.log(`✅ Permission: ${perm}`);
            } else {
              console.error(`❌ Missing permission: ${perm}`);
            }
          }
        }
      } catch (error) {
        console.error(
          `❌ Cannot access channel ${this.channelId}:`,
          error.message
        );

        // List available channels
        console.log("📋 Available channels:");
        guild.channels.cache
          .filter((ch) => ch.isTextBased())
          .forEach((ch) => {
            const perms = ch.permissionsFor(this.client.user);
            const canSend = perms.has(["ViewChannel", "SendMessages"]);
            console.log(`  ${canSend ? "✅" : "❌"} ${ch.name} (${ch.id})`);
          });
      }
    } catch (error) {
      console.error("Error validating bot setup:", error);
    }
  }

  /**
   * Connect to Discord
   */
  async connect() {
    try {
      await this.client.login(config.discord.token);
    } catch (error) {
      console.error("Error connecting to Discord:", error);
      throw error;
    }
  }

  /**
   * Disconnect from Discord
   */
  async disconnect() {
    if (this.client) {
      await this.client.destroy();
    }
  }

  /**
   * Set simulator reference for commands
   */
  setSimulator(simulator) {
    this.simulator = simulator;
  }

  /**
   * Set strategy reference for commands
   */
  setStrategy(strategy) {
    this.strategy = strategy;
  }
}

module.exports = DiscordBot;
